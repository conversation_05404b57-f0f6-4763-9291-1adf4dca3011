import 'package:flutter/material.dart';
import '../screens/admin/settings_screen.dart';
import '../screens/food_price_management_screen.dart';
import '../screens/food_price_export_screen.dart';
import '../screens/grocery_cost_demo_screen.dart';
// Import các màn hình khác ở đây

class AppRoutes {
  static const String settings = '/settings';
  static const String foodPriceManagement = '/food-price-management';
  static const String foodPriceExport = '/food-price-export';
  static const String groceryCostDemo = '/grocery-cost-demo';
  // Các route khác ở đây

  static Map<String, WidgetBuilder> routes = {
    settings: (context) => SettingsScreen(),
    foodPriceManagement: (context) => const FoodPriceManagementScreen(),
    foodPriceExport: (context) => const FoodPriceExportScreen(),
    groceryCostDemo: (context) => const GroceryCostDemoScreen(),
    // Các route khác ở đây
  };
}