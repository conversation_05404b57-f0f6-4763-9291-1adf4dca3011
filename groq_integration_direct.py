import os
import json
import time
import threading
import random
import httpx
from typing import List, Dict, Optional, Tuple
from models import NutritionInfo, Dish, Ingredient

# Helper function to ensure regex operations work
def safe_regex_sub(pattern, replacement, text, flags=0, count=0):
    """Safe regex substitution to prevent 're' variable access errors"""
    try:
        import re as local_re
        if count > 0:
            return local_re.sub(pattern, replacement, text, count=count, flags=flags)
        else:
            return local_re.sub(pattern, replacement, text, flags=flags)
    except Exception as e:
        print(f"⚠️ Regex substitution failed: {e}")
        return text

def safe_regex_findall(pattern, text, flags=0):
    """Safe regex findall to prevent 're' variable access errors"""
    try:
        import re as local_re
        return local_re.findall(pattern, text, flags)
    except Exception as e:
        print(f"⚠️ Regex findall failed: {e}")
        return []

def safe_regex_search(pattern, text, flags=0):
    """Safe regex search to prevent 're' variable access errors"""
    try:
        import re as local_re
        return local_re.search(pattern, text, flags)
    except Exception as e:
        print(f"⚠️ Regex search failed: {e}")
        return None

# Import fallback data
from fallback_meals import FALLBACK_MEALS

class RateLimiter:
    """Manages API rate limits"""
    
    def __init__(self, requests_per_minute: int = 60, requests_per_day: int = 1000):
        """
        Initialize rate limiter
        
        Args:
            requests_per_minute: Maximum requests per minute
            requests_per_day: Maximum requests per day
        """
        self.requests_per_minute = requests_per_minute
        self.requests_per_day = requests_per_day
        self.minute_requests = 0
        self.day_requests = 0
        self.minute_reset_time = time.time() + 60  # Reset after 1 minute
        self.day_reset_time = time.time() + 86400  # Reset after 1 day
        self.lock = threading.Lock()
    
    def can_make_request(self) -> Tuple[bool, int]:
        """
        Check if a request can be made
        
        Returns:
            Tuple[bool, int]: (Can make request, wait time in seconds)
        """
        with self.lock:
            current_time = time.time()
            
            # Reset minute counter if needed
            if current_time > self.minute_reset_time:
                self.minute_requests = 0
                self.minute_reset_time = current_time + 60
            
            # Reset day counter if needed
            if current_time > self.day_reset_time:
                self.day_requests = 0
                self.day_reset_time = current_time + 86400
            
            # Check limits
            if self.minute_requests < self.requests_per_minute and self.day_requests < self.requests_per_day:
                self.minute_requests += 1
                self.day_requests += 1
                return True, 0
            
            # Calculate wait time
            wait_time = min(
                self.minute_reset_time - current_time,
                self.day_reset_time - current_time
            )
            
            # Add jitter to avoid thundering herd
            wait_time += random.uniform(1, 5)
            
            return False, max(1, int(wait_time))

class DirectGroqClient:
    """
    A simple client for the Groq API using direct HTTP requests
    """
    def __init__(self, api_key=None):
        """
        Initialize the client with the given API key
        
        Args:
            api_key (str, optional): The API key to use. If None, uses GROQ_API_KEY env var.
        """
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("No API key provided and GROQ_API_KEY env var not set")
        
        self.base_url = "https://api.groq.com/openai/v1"
        self.client = httpx.Client(timeout=60.0)
        self.client.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def list_models(self):
        """
        List available models
        
        Returns:
            list: A list of available model IDs
        """
        response = self.client.get(f"{self.base_url}/models")
        response.raise_for_status()
        data = response.json()
        return [model["id"] for model in data["data"]]
    
    def generate_completion(self, model, prompt, temperature=0.7, max_tokens=1000, top_p=0.95):
        """
        Generate a completion using the Groq API
        
        Args:
            model (str): The model to use
            prompt (str): The prompt to use
            temperature (float, optional): The temperature. Defaults to 0.7.
            max_tokens (int, optional): The maximum tokens. Defaults to 1000.
            top_p (float, optional): The top_p value. Defaults to 0.95.
            
        Returns:
            dict: The API response
        """
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": top_p
        }
        
        print(f"Sending request to Groq API with model: {model}")
        print(f"Temperature: {temperature}, max_tokens: {max_tokens}, top_p: {top_p}")
        
        response = self.client.post(
            f"{self.base_url}/chat/completions",
            json=payload
        )
        response.raise_for_status()
        
        data = response.json()
        print(f"Raw Groq API response: {data}")
        
        if "choices" in data and len(data["choices"]) > 0:
            content = data["choices"][0]["message"]["content"].strip()
            print(f"Raw content from Groq:\n{content}")
        
        return data
    
    def close(self):
        """Close the HTTP client"""
        if self.client:
            self.client.close()

class GroqService:
    """Integration service with LLaMA 3 via Groq for intelligent meal planning"""
    
    def __init__(self, api_key: str = os.getenv("GROQ_API_KEY")):
        """
        Initialize Groq service with API key
        
        Args:
            api_key: Groq API key, from environment variable if not provided
        """
        self.api_key = api_key
        self.available = bool(api_key)
        
        # Initialize cache and rate limiter
        self.cache = {}
        self.rate_limiter = RateLimiter(requests_per_minute=60, requests_per_day=1000)
        self.max_retries = 3
        
        # Add variables to track quota status
        self.quota_exceeded = False
        self.quota_reset_time = None
        
        # Default model using LLaMA 3
        self.default_model = "llama3-8b-8192"
        self.client = None
        self.model = self.default_model
        
        if self.available:
            try:
                print("\n=== INITIALIZING GROQ SERVICE ===")
                print(f"API Key: {'***' + self.api_key[-4:] if self.api_key else 'None'}")
                
                # Initialize direct client
                try:
                    self.client = DirectGroqClient(api_key=self.api_key)
                except Exception as e:
                    print(f"Error initializing Groq client: {str(e)}")
                    self.available = False
                    return
                
                # Priority list of models to try
                self.preferred_models = [
                    "llama3-70b-8192",  # LLaMA 3 70B - Strongest model
                    "llama3-8b-8192",   # LLaMA 3 8B - Balance of speed and performance
                    "mixtral-8x7b-32768"  # Mixtral - Fallback if LLaMA is not available
                ]
                
                # Check available models
                if self.client:
                    try:
                        print("Fetching available models...")
                        available_models = self.client.list_models()
                        
                        print("Available models:")
                        for model_name in available_models:
                            print(f"- {model_name}")
                        
                        # Find first available preferred model
                        selected_model = None
                        for model_name in self.preferred_models:
                            if model_name in available_models:
                                selected_model = model_name
                                break
                        
                        # If no preferred model found, use default model
                        if not selected_model:
                            selected_model = self.default_model
                        
                        self.model = selected_model
                        print(f"Using model: {self.model}")
                        
                    except Exception as e:
                        print(f"Error fetching models: {str(e)}")
                        print(f"Using default model: {self.default_model}")
                        self.model = self.default_model
                
                print("Groq initialization successful")
                print("=== GROQ SERVICE INITIALIZED ===\n")
            except Exception as e:
                print(f"ERROR initializing Groq: {str(e)}")
                import traceback
                traceback.print_exc()
                self.available = False
    
    def __del__(self):
        """Close the client when the object is deleted"""
        if hasattr(self, 'client') and self.client:
            try:
                self.client.close()
            except:
                pass
        
    def generate_meal_suggestions(
        self,
        calories_target: int,
        protein_target: int,
        fat_target: int,
        carbs_target: int,
        meal_type: str,
        preferences: List[str] = None,
        allergies: List[str] = None,
        cuisine_style: str = None,
        use_ai: bool = True,  # Parameter to disable AI
        day_of_week: str = None,  # Thêm ngày để tăng tính đa dạng
        random_seed: int = None,  # Thêm random seed để tăng tính đa dạng
        user_data: Dict = None  # Add parameter for user data
    ) -> List[Dict]:
        """
        Generate meal suggestions based on nutritional targets and preferences
        
        Args:
            calories_target: Target calories for the meal
            protein_target: Target protein for the meal (g)
            fat_target: Target fat for the meal (g)
            carbs_target: Target carbs for the meal (g)
            meal_type: Type of meal (breakfast, lunch, dinner)
            preferences: Food preferences (optional)
            allergies: Food allergies to avoid (optional)
            cuisine_style: Preferred cuisine style (optional)
            use_ai: Whether to use AI for generation
            day_of_week: Day of the week (optional, for diversity)
            random_seed: Random seed (optional, for diversity)
            user_data: Dictionary containing user demographic and goal info (optional)
            
        Returns:
            List of meal suggestion dictionaries
        """
        # Nếu không sử dụng AI hoặc dịch vụ không khả dụng, trả về fallback
        if not use_ai or not self.available:
            print(f"Not using AI for meal suggestions. use_ai={use_ai}, available={self.available}")
            return self._fallback_meal_suggestions(meal_type)
        
        # Kiểm tra xem có vượt quá quota không
        if self.quota_exceeded:
            current_time = time.time()
            if self.quota_reset_time and current_time < self.quota_reset_time:
                wait_time = int(self.quota_reset_time - current_time)
                print(f"Quota exceeded, waiting {wait_time} seconds until reset")
                return self._fallback_meal_suggestions(meal_type)
            else:
                # Reset quota status
                self.quota_exceeded = False
                self.quota_reset_time = None
        
        # Kiểm tra rate limiter
        can_request, wait_time = self.rate_limiter.can_make_request()
        if not can_request:
            print(f"Rate limit reached, waiting {wait_time} seconds")
            return self._fallback_meal_suggestions(meal_type)
        
        # Tạo cache key
        cache_key = f"{meal_type}_{calories_target}_{protein_target}_{fat_target}_{carbs_target}"
        if preferences:
            cache_key += f"_pref={'_'.join(sorted(preferences))}"
        if allergies:
            cache_key += f"_allergy={'_'.join(sorted(allergies))}"
        if cuisine_style:
            cache_key += f"_cuisine={cuisine_style}"
        if day_of_week:
            cache_key += f"_day={day_of_week}"
        if random_seed:
            cache_key += f"_seed={random_seed}"
        if user_data:
            # Add user data to cache key
            user_data_str = "_".join([f"{k}:{v}" for k, v in user_data.items() if k in ['gender', 'age', 'goal', 'activity_level']])
            cache_key += f"_user:{user_data_str}"
        
        # DISABLE CACHE để luôn tạo món mới
        # Thêm thời gian hiện tại vào cache key để đảm bảo luôn có kết quả mới
        current_time = int(time.time() / 60)  # Thay đổi mỗi phút để tăng đa dạng
        cache_key += f"_time={current_time}"

        # TEMPORARILY DISABLE CACHE để test đa dạng món ăn
        # if cache_key in self.cache:
        #     print(f"Using cached meal suggestions for {meal_type}")
        #     return self.cache[cache_key]
        print(f"Generating fresh meal suggestions for {meal_type} (cache disabled)")
        
        # Chuẩn bị prompt
        prompt = self._prepare_meal_prompt(
            calories_target, protein_target, fat_target, carbs_target,
            meal_type, preferences, allergies, cuisine_style, day_of_week, random_seed, user_data
        )
        
        # Tăng số lần thử lại lên 5 để đảm bảo có kết quả
        max_retries = 5
        for attempt in range(max_retries):
            try:
                print(f"Generating meal suggestions for {meal_type} (attempt {attempt+1}/{max_retries})")
                
                # Thay đổi temperature và top_p mỗi lần thử lại để tăng khả năng thành công
                temperature = 0.7 + (attempt * 0.05)  # Tăng dần từ 0.7 đến 0.9
                top_p = 0.95 - (attempt * 0.05)  # Giảm dần từ 0.95 đến 0.75
                
                # Gọi API
                response = self.client.generate_completion(
                    model=self.model,
                    prompt=prompt,
                    temperature=temperature,
                    max_tokens=2000,
                    top_p=top_p
                )
                
                # Xử lý response
                if "choices" in response and len(response["choices"]) > 0:
                    content = response["choices"][0]["message"]["content"].strip()
                    
                    # Phân tích JSON từ response
                    meal_data = self._extract_json_from_response(content)
                    
                    if meal_data and isinstance(meal_data, list) and len(meal_data) > 0:
                        # Validate and process meal data
                        validated_meals = self._validate_meals(meal_data)
                        
                        if validated_meals:
                            print(f"Successfully generated {len(validated_meals)} meal suggestions")
                            # Cache kết quả
                            self.cache[cache_key] = validated_meals
                            return validated_meals
                        else:
                            print("Validation failed for meal data")
                    else:
                        print("No valid meal data in response")
                else:
                    print("No choices in response")
                
            except Exception as e:
                print(f"Error generating meal suggestions (attempt {attempt+1}): {str(e)}")
                # Kiểm tra xem có phải lỗi quota không
                if "quota exceeded" in str(e).lower() or "rate limit" in str(e).lower():
                    print("Quota exceeded or rate limit reached")
                    self.quota_exceeded = True
                    self.quota_reset_time = time.time() + 3600  # Reset sau 1 giờ
                    break
                
                # Chờ trước khi thử lại
                wait_time = 2 ** attempt  # Exponential backoff
                print(f"Waiting {wait_time} seconds before retry")
                time.sleep(wait_time)
        
        # Nếu tất cả các lần thử đều thất bại, trả về fallback
        print(f"All {max_retries} attempts failed, using fallback meals")
        fallback_meals = self._fallback_meal_suggestions(meal_type)
        
        # Thêm thông tin về ngày để tăng tính đa dạng
        if day_of_week and fallback_meals:
            for meal in fallback_meals:
                if "name" in meal and day_of_week not in meal["name"]:
                    meal["name"] = f"{meal['name']} ({day_of_week})"
        
        return fallback_meals
    
    def _validate_meals(self, meal_data: List[Dict]) -> List[Dict]:
        """
        Validate meal data and ensure it has the expected structure
        
        Args:
            meal_data: List of meal dictionaries to validate
            
        Returns:
            List of validated meal dictionaries
        """
        valid_meals = []
        
        for meal in meal_data:
            if not isinstance(meal, dict):
                print(f"Skipping non-dict meal: {meal}")
                continue
                
            if 'name' not in meal:
                print(f"Skipping meal without name: {meal}")
                continue
                
            # Ensure ingredients list exists
            if 'ingredients' not in meal or not isinstance(meal['ingredients'], list):
                print(f"Adding empty ingredients list to meal: {meal['name']}")
                meal['ingredients'] = []
                
            # Kiểm tra và chuyển đổi trường preparation thành List[str]
            if 'preparation' not in meal:
                meal['preparation'] = [f"Prepare {meal['name']} with the listed ingredients."]
            elif isinstance(meal['preparation'], str):
                # Nếu là chuỗi, chuyển thành danh sách với một phần tử
                meal['preparation'] = [meal['preparation']]
            elif isinstance(meal['preparation'], list):
                # Nếu là danh sách, đảm bảo tất cả các phần tử đều là chuỗi
                meal['preparation'] = [str(step) for step in meal['preparation']]
            else:
                # Nếu là kiểu dữ liệu khác, đặt giá trị mặc định
                print(f"Invalid preparation format for meal: {meal['name']}, replacing with default")
                meal['preparation'] = [f"Prepare {meal['name']} with the listed ingredients."]
            
            # Ensure ingredients is not empty
            if not meal['ingredients']:
                meal['ingredients'] = [{'name': 'Main ingredient', 'amount': '100g'}]
            
            # Ensure nutrition exists
            if 'nutrition' not in meal:
                print(f"Adding default nutrition to meal: {meal['name']}")
                meal['nutrition'] = {
                    'calories': 400, 
                    'protein': 20, 
                    'fat': 15, 
                    'carbs': 45
                }
            
            # Kiểm tra và đặt giá trị mặc định cho trường preparation_time nếu cần
            if 'preparation_time' not in meal or not meal['preparation_time']:
                meal['preparation_time'] = "30-45 phút"
                print(f"Adding default preparation time to meal: {meal['name']}")
            
            # Kiểm tra và đặt giá trị mặc định cho trường health_benefits nếu cần
            if 'health_benefits' not in meal or not meal['health_benefits']:
                meal['health_benefits'] = f"Món ăn {meal['name']} cung cấp đầy đủ dinh dưỡng cần thiết và năng lượng cân bằng cho cơ thể."
                print(f"Adding default health benefits to meal: {meal['name']}")
            
            valid_meals.append(meal)
            
        print(f"Validated {len(valid_meals)} out of {len(meal_data)} meals")
        return valid_meals
    
    def _get_fallback_meals(self, meal_type: str) -> List[Dict]:
        """
        Get fallback meal data
        
        Args:
            meal_type: Meal type (breakfast, lunch, dinner)
            
        Returns:
            List of fallback meals
        """
        meal_type_lower = meal_type.lower()
        
        if "breakfast" in meal_type_lower or "morning" in meal_type_lower:
            return FALLBACK_MEALS.get("breakfast", [])
        elif "lunch" in meal_type_lower or "noon" in meal_type_lower:
            return FALLBACK_MEALS.get("lunch", [])
        elif "dinner" in meal_type_lower or "evening" in meal_type_lower:
            return FALLBACK_MEALS.get("dinner", [])
        else:
            # Return a mix of meals
            all_meals = []
            for meals_list in FALLBACK_MEALS.values():
                all_meals.extend(meals_list)
            
            # Shuffle the list to get random ones
            random.shuffle(all_meals)
            return all_meals[:2]  # Return maximum 1-2 meals
    
    def _fallback_meal_suggestions(self, meal_type: str) -> List[Dict]:
        """
        Return fallback data for meal type
        
        Args:
            meal_type: Meal type
            
        Returns:
            List of fallback meals
        """
        return self._get_fallback_meals(meal_type)
    
    def clear_cache(self):
        """Clear cache to force new data creation"""
        print("Clearing Groq service cache")
        self.cache = {}
    
    def get_cache_info(self):
        """
        Get information about the cache
        
        Returns:
            Cache information
        """
        return {
            "num_entries": len(self.cache),
            "keys": list(self.cache.keys())
        }
    
    def _extract_json_from_response(self, response_text: str) -> List[Dict]:
        """
        Trích xuất dữ liệu JSON từ phản hồi của AI
        
        Args:
            response_text: Văn bản phản hồi từ API
            
        Returns:
            List[Dict]: Dữ liệu món ăn dạng JSON hoặc None nếu không thể phân tích
        """
        try:
            # Phương pháp 1: Thử phân tích toàn bộ phản hồi là JSON
            print("Trying to parse entire response as JSON...")
            meal_data = json.loads(response_text)
            if isinstance(meal_data, list) and len(meal_data) > 0:
                print(f"Successfully parsed entire response as JSON array with {len(meal_data)} items")
                return meal_data
        except json.JSONDecodeError:
            print("Entire response is not valid JSON, trying to fix format...")
            
            # Phương pháp 1.5: Sửa lỗi cụ thể - chuyển đổi {"String value", ...} thành {"name": "String value", ...}
            try:
                fixed_text = safe_regex_sub(r'{\s*"([^"]+)",', r'{"name": "\1",', response_text)
                meal_data = json.loads(fixed_text)
                if isinstance(meal_data, list) and len(meal_data) > 0:
                    print(f"Successfully parsed fixed JSON with {len(meal_data)} items")
                    return meal_data
            except json.JSONDecodeError:
                print("Format fixing failed, trying JSON extraction...")

            # Phương pháp 2: Trích xuất JSON sử dụng regex
            json_pattern = r'\[\s*\{.*\}\s*\]'
            matches = safe_regex_search(json_pattern, response_text, 16)  # re.DOTALL = 16
            if matches:
                json_str = matches.group(0)
                print(f"Found JSON-like pattern: {json_str[:100]}...")
                try:
                    meal_data = json.loads(json_str)
                    if isinstance(meal_data, list) and len(meal_data) > 0:
                        print(f"Successfully parsed extracted JSON with {len(meal_data)} items")
                        return meal_data
                except json.JSONDecodeError:
                    print("Extracted pattern is not valid JSON")
            
                # Phương pháp 2.5: Sửa lỗi cụ thể cho pattern đã tìm thấy
                try:
                    fixed_text = safe_regex_sub(r'{\s*"([^"]+)",', r'{"name": "\1",', json_str)
                    meal_data = json.loads(fixed_text)
                    if isinstance(meal_data, list) and len(meal_data) > 0:
                        print(f"Successfully parsed fixed JSON pattern with {len(meal_data)} items")
                        return meal_data
                except json.JSONDecodeError:
                    print("Fixed JSON pattern is still invalid")
            
            # Phương pháp 3: Tìm mảng JSON giữa dấu ngoặc vuông
            json_start = response_text.find("[")
            json_end = response_text.rfind("]") + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                print(f"Extracted JSON between brackets: {json_str[:100]}...")
                try:
                    meal_data = json.loads(json_str)
                    if isinstance(meal_data, list) and len(meal_data) > 0:
                        print(f"Successfully parsed extracted JSON array with {len(meal_data)} items")
                        return meal_data
                except json.JSONDecodeError:
                    print("Extracted JSON array is invalid, trying to fix...")
                    
                    # Phương pháp 3.5: Sửa lỗi cụ thể cho mảng đã tìm thấy
                    try:
                        fixed_text = safe_regex_sub(r'{\s*"([^"]+)",', r'{"name": "\1",', json_str)
                        meal_data = json.loads(fixed_text)
                        if isinstance(meal_data, list) and len(meal_data) > 0:
                            print(f"Successfully parsed fixed JSON array with {len(meal_data)} items")
                            return meal_data
                    except json.JSONDecodeError:
                        print("Error parsing fixed JSON array")
            
            # Phương pháp 4: Tạo dữ liệu thủ công từ các pattern có thể nhận ra
            print("Trying manual extraction...")
            result = []
            try:
                # Parse các dish riêng lẻ
                dish_texts = safe_regex_findall(r'{\s*(?:"[^"]+"|"name":\s*"[^"]+")(.*?)},?(?=\s*{|\s*\])', response_text, 16)  # re.DOTALL = 16
                
                for dish_text in dish_texts:
                    full_text = "{" + dish_text + "}"
                    dish = {}
                    
                    # Extract name
                    name_match = safe_regex_search(r'^\s*"([^"]+)"', dish_text)
                    name_key_match = safe_regex_search(r'"name":\s*"([^"]+)"', full_text)
                    
                    if name_match:
                        dish['name'] = name_match.group(1)
                    elif name_key_match:
                        dish['name'] = name_key_match.group(1)
                    else:
                        continue
                    
                    # Extract description
                    desc_match = safe_regex_search(r'"description":\s*"([^"]+)"', full_text)
                    if desc_match:
                        dish['description'] = desc_match.group(1)

                    # Extract ingredients
                    ingredients = []
                    ingredients_block = safe_regex_search(r'"ingredients":\s*\[(.*?)\]', full_text, 16)  # re.DOTALL = 16
                    if ingredients_block:
                        ingredients_text = ingredients_block.group(1)
                        ingredient_matches = safe_regex_findall(r'{\s*"name":\s*"([^"]+)",\s*"amount":\s*"([^"]+)"\s*}', ingredients_text)
                        for name, amount in ingredient_matches:
                            ingredients.append({"name": name, "amount": amount})
                    dish['ingredients'] = ingredients if ingredients else [{"name": "Nguyên liệu chính", "amount": "100g"}]
                    
                    # Extract preparation steps
                    preparation = []
                    prep_block = safe_regex_search(r'"preparation":\s*\[(.*?)\]', full_text, 16)  # re.DOTALL = 16
                    if prep_block:
                        prep_text = prep_block.group(1)
                        step_matches = safe_regex_findall(r'"([^"]+)"', prep_text)
                        for step in step_matches:
                            preparation.append(step)
                    dish['preparation'] = preparation if preparation else ["Chế biến theo hướng dẫn"]
                    
                    # Extract nutrition
                    nutrition = {}
                    nutrition_block = safe_regex_search(r'"nutrition":\s*{\s*(.*?)\s*}', full_text, 16)  # re.DOTALL = 16
                    if nutrition_block:
                        nutrition_text = nutrition_block.group(1)

                        for key in ["calories", "protein", "fat", "carbs"]:
                            value_match = safe_regex_search(fr'"{key}":\s*(\d+)', nutrition_text)
                            if value_match:
                                nutrition[key] = int(value_match.group(1))
                    
                    dish['nutrition'] = nutrition if nutrition else {"calories": 400, "protein": 25, "fat": 15, "carbs": 45}
                    
                    # Extract other fields
                    for field in ["preparation_time", "health_benefits"]:
                        field_match = safe_regex_search(fr'"{field}":\s*"([^"]+)"', full_text)
                        if field_match:
                            dish[field] = field_match.group(1)
                    
                    if 'preparation_time' not in dish:
                        dish['preparation_time'] = "30 phút"
                    if 'health_benefits' not in dish:
                        dish['health_benefits'] = f"Món ăn {dish['name']} cung cấp dinh dưỡng cân bằng cho cơ thể"
                    
                    # Add to result if has minimum required fields
                    if dish.get('name'):
                        result.append(dish)
                
                if result:
                    print(f"Manual extraction succeeded, created {len(result)} items")
                    return result
            except Exception as e:
                print(f"Error during manual extraction: {str(e)}")
        
        # Không tìm thấy JSON hợp lệ
        return None
    
    def _prepare_meal_prompt(
        self,
        calories_target: int,
        protein_target: int,
        fat_target: int,
        carbs_target: int,
        meal_type: str,
        preferences: List[str] = None,
        allergies: List[str] = None,
        cuisine_style: str = None,
        day_of_week: str = None,
        random_seed: int = None,
        user_data: Dict = None
    ) -> str:
        """
        Chuẩn bị prompt cho AI để tạo gợi ý món ăn
        
        Args:
            calories_target: Mục tiêu calo
            protein_target: Mục tiêu protein (g)
            fat_target: Mục tiêu chất béo (g)
            carbs_target: Mục tiêu carbs (g)
            meal_type: Loại bữa ăn (breakfast, lunch, dinner)
            preferences: Danh sách sở thích thực phẩm (tùy chọn)
            allergies: Danh sách dị ứng thực phẩm (tùy chọn)
            cuisine_style: Phong cách ẩm thực (tùy chọn)
            day_of_week: Ngày trong tuần (tùy chọn)
            random_seed: Seed ngẫu nhiên (tùy chọn)
            user_data: Dictionary containing user demographic and goal info (optional)
            
        Returns:
            str: Prompt cho AI
        """
        preferences_str = ", ".join(preferences) if preferences else "none"
        allergies_str = ", ".join(allergies) if allergies else "none"
        cuisine_style_str = cuisine_style if cuisine_style else "no specific requirement"
        day_str = f" for {day_of_week}" if day_of_week else ""
        
        # Thêm seed ngẫu nhiên và ngày vào prompt để tăng tính đa dạng
        diversity_str = ""
        if random_seed:
            diversity_str = f"\n- Random seed: {random_seed}"
        if day_of_week:
            diversity_str += f"\n- Day of week: {day_of_week}"
            
        # Extract user data information
        user_info = ""
        if user_data:
            gender = user_data.get('gender', 'unknown')
            age = user_data.get('age', 'unknown')
            goal = user_data.get('goal', 'unknown')
            activity_level = user_data.get('activity_level', 'unknown')
            
            user_info = f"""
- User gender: {gender}
- User age: {age}
- User goal: {goal}
- User activity level: {activity_level}"""
        
        # Danh sách món ăn đa dạng để AI tham khảo
        diverse_dishes = {
            "bữa sáng": ["Phở bò", "Bún bò Huế", "Cháo gà", "Bánh cuốn", "Xôi xéo", "Bánh mì pate", "Cháo đậu xanh", "Bánh bao", "Chè đậu đỏ", "Bánh flan"],
            "bữa trưa": ["Cơm tấm", "Bún chả", "Mì Quảng", "Cao lầu", "Bánh xèo", "Gỏi cuốn", "Cơm gà", "Bún riêu", "Bánh canh", "Cơm chiên"],
            "bữa tối": ["Lẩu thái", "Cá kho tộ", "Thịt kho tàu", "Canh chua", "Gà nướng", "Tôm rang me", "Sườn xào chua ngọt", "Cà ri gà", "Bò lúc lắc", "Chả cá"]
        }

        suggested_dishes = diverse_dishes.get(meal_type.lower(), diverse_dishes["bữa sáng"])
        dishes_suggestion = f"\n\nMột số gợi ý món ăn {meal_type}: {', '.join(suggested_dishes[:5])}. Hãy tạo món KHÁC với những món này để tăng tính đa dạng."

        # Prompt được cải tiến để "ép" AI tuân thủ quy tắc JSON
        prompt = f"""Bạn là một chuyên gia dinh dưỡng. Dựa trên các thông tin sau: {meal_type}{day_str} với mục tiêu dinh dưỡng {calories_target}kcal, {protein_target}g protein, {fat_target}g chất béo, {carbs_target}g carbs, sở thích: {preferences_str}, dị ứng: {allergies_str}, phong cách ẩm thực: {cuisine_style_str}{diversity_str}{user_info}{dishes_suggestion}, hãy tạo ra một danh sách gồm 1-2 món ăn Việt Nam.

YÊU CẦU TUYỆT ĐỐI:

Phản hồi của bạn CHỈ VÀ CHỈ được chứa một chuỗi JSON hợp lệ.

Không được thêm bất kỳ văn bản, lời chào, ghi chú hay định dạng markdown nào khác như ```json ở đầu hoặc cuối.

Chuỗi JSON phải là một mảng (array) các đối tượng (object).

Mỗi đối tượng món ăn BẮT BUỘC phải có đầy đủ các key sau với đúng kiểu dữ liệu: name (string), description (string), ingredients (array of objects), preparation (array of strings), nutrition (object), preparation_time (string), health_benefits (string).

Bên trong ingredients, mỗi đối tượng phải có name (string) và amount (string).

Bên trong nutrition, mỗi đối tượng phải có calories (number), protein (number), fat (number), và carbs (number).

Đây là một ví dụ về một đối tượng món ăn hợp lệ để bạn tuân theo:

{{
  "name": "Cơm Tấm Sườn Nướng",
  "description": "Món cơm tấm truyền thống với sườn nướng thơm ngon, chả trứng và nước mắm chua ngọt.",
  "ingredients": [
    {{"name": "Cơm tấm", "amount": "150g"}},
    {{"name": "Sườn heo", "amount": "100g"}},
    {{"name": "Trứng gà", "amount": "1 quả"}},
    {{"name": "Nước mắm", "amount": "2 thìa canh"}}
  ],
  "preparation": [
    "Bước 1: Ướp sườn với nước mắm, đường, tỏi băm, tiêu trong 30 phút.",
    "Bước 2: Nướng sườn trên than hoa hoặc lò nướng ở 200°C trong 15-20 phút, lật đều 2 mặt.",
    "Bước 3: Đánh trứng với chút muối, chiên thành chả mỏng rồi cắt thành miếng.",
    "Bước 4: Nấu cơm tấm với tỷ lệ nước vừa đủ để cơm dẻo nhưng không bị nhão.",
    "Bước 5: Bày cơm tấm ra đĩa, xếp sườn nướng và chả trứng lên trên, ăn kèm với dưa chua và nước mắm pha."
  ],
  "nutrition": {{
    "calories": {calories_target},
    "protein": {protein_target},
    "fat": {fat_target},
    "carbs": {carbs_target}
  }},
  "preparation_time": "45 phút",
  "health_benefits": "Cung cấp protein chất lượng cao từ thịt heo và trứng, carbs từ cơm tấm giúp bổ sung năng lượng, phù hợp cho mục tiêu dinh dưỡng của người dùng."
}}

QUY TẮC BỔ SUNG:
1. Tất cả tên món ăn và mô tả phải bằng tiếng Việt
2. Nguyên liệu phải có tên tiếng Việt và lượng cụ thể (gram, thìa, quả...)
3. Hướng dẫn chuẩn bị phải bằng tiếng Việt với các bước chi tiết:
   - Mỗi bước phải bắt đầu bằng "Bước 1:", "Bước 2:", v.v.
   - Bao gồm thời gian nấu, nhiệt độ nếu cần
   - Mô tả cụ thể cách thức chế biến (ướp, nướng, xào, luộc...)
   - Không được viết "Chế biến theo hướng dẫn" hay các câu chung chung
4. Tạo các món ăn KHÁC NHAU và sáng tạo, tránh lặp lại món cũ
5. KHÔNG bao gồm tên ngày trong tên món ăn
6. Xem xét mục tiêu cụ thể của người dùng:
   - Giảm cân: Tập trung vào món ăn no bụng, nhiều chất xơ, protein cao, ít calo
   - Tăng cân: Tập trung vào món ăn giàu protein, dinh dưỡng dày đặc
   - Sức khỏe tổng quát: Tập trung vào món ăn cân bằng, đa dạng dinh dưỡng
7. Luôn bao gồm thời gian chuẩn bị cho mỗi món (ví dụ: "25-30 phút")
8. Luôn bao gồm lợi ích sức khỏe cụ thể của mỗi món
9. Đa dạng hóa món ăn: không chỉ bánh mì, hãy tạo cháo, phở, cơm, bún, v.v.

Bây giờ, hãy tạo danh sách món ăn của bạn."""
        
        return prompt

# Initialize service singleton
groq_service = GroqService() 