rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // ===== USER DATA RULES =====
    // Cho phép user đọc/ghi dữ liệu của ch<PERSON>h họ
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Subcollections của user
      match /meal_plans/{planId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /food_entries/{entryId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /exercise_entries/{entryId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /water_entries/{entryId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /progress/{progressId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /favorites/{favoriteId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /nutrition_goals/{goalId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // ===== MEAL PLANS COLLECTION =====
    // Cho phép đọc/ghi kế hoạch ăn của người dùng
    match /meal_plans/{planId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.user_id == request.auth.uid);
      allow create: if request.auth != null && 
        request.resource.data.user_id == request.auth.uid;
    }
    
    // ===== FOOD RECORDS COLLECTION =====
    // Cho phép user đọc/ghi food records của chính họ
    match /food_records/{recordId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.user_id == request.auth.uid);
      allow create: if request.auth != null && 
        request.resource.data.user_id == request.auth.uid;
    }
    
    // ===== VIETNAMESE FOOD PRICES =====
    // Cho phép tất cả user đã đăng nhập đọc và đóng góp giá
    match /vietnamese_food_prices/{priceId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // ===== PUBLIC DATA =====
    // Cho phép đọc dữ liệu thực phẩm công khai
    match /food_items/{foodId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Cho phép đọc dữ liệu bài tập công khai
    match /exercise_types/{exerciseId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // ===== ADMIN DATA =====
    // Chỉ admin mới có thể truy cập
    match /admin/{document=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.admin == true;
    }
    
    // ===== SYSTEM DATA =====
    // Cho phép đọc dữ liệu hệ thống
    match /system/{systemId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.token.admin == true;
    }
    
    // ===== NUTRITION CACHE =====
    // Cache dữ liệu dinh dưỡng - cho phép đọc/ghi khi đã đăng nhập
    match /nutrition_cache/{cacheId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // ===== LATEST MEAL PLANS =====
    // Kế hoạch ăn mới nhất của user
    match /latest_meal_plans/{userId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.user_id == request.auth.uid || request.auth.uid == userId);
      allow create: if request.auth != null && 
        (request.resource.data.user_id == request.auth.uid || request.auth.uid == userId);
    }
    
    // ===== USER PROFILES =====
    // Profile của user
    match /user_profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // ===== SHARED DATA =====
    // Dữ liệu chia sẻ giữa users (ví dụ: recipes, tips)
    match /shared_recipes/{recipeId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (resource == null || resource.data.created_by == request.auth.uid);
    }
    
    match /health_tips/{tipId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // ===== FALLBACK RULE =====
    // Cho phép đọc/ghi tất cả dữ liệu khi đã đăng nhập (temporary for development)
    // REMOVE THIS IN PRODUCTION!
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}

// ===== FIREBASE STORAGE RULES =====
// Thêm rules cho Firebase Storage (nếu sử dụng)
/*
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Cho phép user upload/download files của chính họ
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Cho phép upload hình ảnh thực phẩm
    match /food_images/{imageId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Cho phép upload avatar
    match /avatars/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
*/
