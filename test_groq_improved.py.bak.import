#!/usr/bin/env python3
"""
Test script để kiểm tra hệ thống Groq đã đư<PERSON> cả<PERSON> tiến
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from groq_integration_direct import GroqService  # Fixed version

def print_separator(title):
    """In separator với title"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_subsection(title):
    """In subsection header"""
    print(f"\n--- {title} ---")

def test_meal_suggestions():
    """Test meal suggestions với các scenario khác nhau"""
    print_separator("🧪 TESTING GROQ IMPROVED SYSTEM")
    
    # Khởi tạo service
    groq_service = GroqService()
    
    # Test cases
    test_cases = [
        {
            "name": "Bữa sáng giảm cân",
            "meal_type": "bữa sáng",
            "calories_target": 300,
            "protein_target": 20,
            "fat_target": 10,
            "carbs_target": 30,
            "preferences": ["healthy", "low-calorie"],
            "allergies": [],
            "cuisine_style": "Vietnamese"
        },
        {
            "name": "Bữa trưa tăng cân",
            "meal_type": "bữa trưa", 
            "calories_target": 600,
            "protein_target": 35,
            "fat_target": 25,
            "carbs_target": 70,
            "preferences": ["high-protein", "nutritious"],
            "allergies": ["seafood"],
            "cuisine_style": "Vietnamese"
        },
        {
            "name": "Bữa tối cân bằng",
            "meal_type": "bữa tối",
            "calories_target": 450,
            "protein_target": 25,
            "fat_target": 15,
            "carbs_target": 50,
            "preferences": ["balanced"],
            "allergies": ["nuts"],
            "cuisine_style": "Vietnamese"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print_subsection(f"Test Case {i}: {test_case['name']}")
        
        try:
            print(f"📋 Parameters:")
            print(f"   Meal Type: {test_case['meal_type']}")
            print(f"   Calories: {test_case['calories_target']}kcal")
            print(f"   Protein: {test_case['protein_target']}g")
            print(f"   Fat: {test_case['fat_target']}g")
            print(f"   Carbs: {test_case['carbs_target']}g")
            print(f"   Preferences: {test_case['preferences']}")
            print(f"   Allergies: {test_case['allergies']}")
            
            print(f"\n🚀 Calling Groq API...")
            
            # Gọi API
            meals = groq_service.generate_meal_suggestions(
                meal_type=test_case['meal_type'],
                calories_target=test_case['calories_target'],
                protein_target=test_case['protein_target'],
                fat_target=test_case['fat_target'],
                carbs_target=test_case['carbs_target'],
                preferences=test_case['preferences'],
                allergies=test_case['allergies'],
                cuisine_style=test_case['cuisine_style']
            )
            
            if meals:
                print(f"✅ SUCCESS: Received {len(meals)} meals")
                
                # Validate kết quả
                validation_result = validate_meal_structure(meals)
                
                results.append({
                    "test_case": test_case['name'],
                    "status": "SUCCESS",
                    "meals_count": len(meals),
                    "validation": validation_result,
                    "meals": meals
                })
                
                # In chi tiết meals
                for j, meal in enumerate(meals, 1):
                    print(f"\n   🍽️ Meal {j}: {meal.get('name', 'Unknown')}")
                    print(f"      Description: {meal.get('description', 'N/A')[:100]}...")
                    print(f"      Ingredients: {len(meal.get('ingredients', []))} items")
                    print(f"      Preparation steps: {len(meal.get('preparation', []))}")
                    print(f"      Nutrition: {meal.get('nutrition', {})}")
                    print(f"      Prep time: {meal.get('preparation_time', 'N/A')}")
                    print(f"      Health benefits: {meal.get('health_benefits', 'N/A')[:80]}...")
                
            else:
                print(f"❌ FAILED: No meals returned")
                results.append({
                    "test_case": test_case['name'],
                    "status": "FAILED",
                    "error": "No meals returned"
                })
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            results.append({
                "test_case": test_case['name'],
                "status": "ERROR",
                "error": str(e)
            })
    
    return results

def validate_meal_structure(meals):
    """Validate cấu trúc của meals"""
    required_fields = ['name', 'description', 'ingredients', 'preparation', 'nutrition', 'preparation_time', 'health_benefits']
    validation_results = []
    
    for i, meal in enumerate(meals):
        meal_validation = {
            "meal_index": i,
            "name": meal.get('name', 'Unknown'),
            "missing_fields": [],
            "field_types": {},
            "is_valid": True
        }
        
        # Check required fields
        for field in required_fields:
            if field not in meal:
                meal_validation["missing_fields"].append(field)
                meal_validation["is_valid"] = False
            else:
                meal_validation["field_types"][field] = type(meal[field]).__name__
        
        # Check specific field types
        if 'ingredients' in meal:
            if not isinstance(meal['ingredients'], list):
                meal_validation["is_valid"] = False
                meal_validation["field_types"]["ingredients"] += " (should be list)"
        
        if 'preparation' in meal:
            if not isinstance(meal['preparation'], list):
                meal_validation["is_valid"] = False
                meal_validation["field_types"]["preparation"] += " (should be list)"
        
        if 'nutrition' in meal:
            if not isinstance(meal['nutrition'], dict):
                meal_validation["is_valid"] = False
                meal_validation["field_types"]["nutrition"] += " (should be dict)"
            else:
                nutrition_fields = ['calories', 'protein', 'fat', 'carbs']
                for nf in nutrition_fields:
                    if nf not in meal['nutrition']:
                        meal_validation["missing_fields"].append(f"nutrition.{nf}")
                        meal_validation["is_valid"] = False
        
        validation_results.append(meal_validation)
    
    return validation_results

def print_test_summary(results):
    """In tóm tắt kết quả test"""
    print_separator("📊 TEST SUMMARY")
    
    total_tests = len(results)
    successful_tests = len([r for r in results if r["status"] == "SUCCESS"])
    failed_tests = len([r for r in results if r["status"] == "FAILED"])
    error_tests = len([r for r in results if r["status"] == "ERROR"])
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Successful: {successful_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"🔥 Errors: {error_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    print_subsection("Detailed Results")
    
    for result in results:
        status_emoji = {"SUCCESS": "✅", "FAILED": "❌", "ERROR": "🔥"}[result["status"]]
        print(f"{status_emoji} {result['test_case']}: {result['status']}")
        
        if result["status"] == "SUCCESS":
            print(f"   📊 Meals generated: {result['meals_count']}")
            
            # Validation summary
            validation = result["validation"]
            valid_meals = len([v for v in validation if v["is_valid"]])
            print(f"   ✅ Valid meals: {valid_meals}/{len(validation)}")
            
            for v in validation:
                if not v["is_valid"]:
                    print(f"   ⚠️  {v['name']}: Missing {v['missing_fields']}")
        
        elif result["status"] in ["FAILED", "ERROR"]:
            print(f"   💥 Error: {result.get('error', 'Unknown error')}")
    
    # Recommendations
    print_subsection("🎯 Recommendations")
    
    if successful_tests == total_tests:
        print("🎉 All tests passed! The improved Groq system is working perfectly.")
    elif successful_tests > 0:
        print(f"✅ {successful_tests}/{total_tests} tests passed. System is mostly working.")
        print("🔧 Consider investigating failed cases for further improvements.")
    else:
        print("❌ All tests failed. Check API key, network connection, and prompt format.")

def save_test_results(results):
    """Lưu kết quả test vào file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"groq_test_results_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 Test results saved to: {filename}")

def main():
    """Main function"""
    print("🚀 Starting Groq Improved System Test")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Run tests
        results = test_meal_suggestions()
        
        # Print summary
        print_test_summary(results)
        
        # Save results
        save_test_results(results)
        
        print(f"\n⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Test finished successfully!")
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
