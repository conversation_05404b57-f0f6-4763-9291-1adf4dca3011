import 'dart:convert';
import 'dart:math';
import '../services/vietnamese_food_price_service.dart';
import '../utils/chat_api.dart';

/// Service AI để phân tích giá cả thực phẩm thông minh
class PriceAIAnalysisService {
  final VietnameseFoodPriceService _priceService = VietnameseFoodPriceService();
  final ChatAPI _chatAPI = ChatAPI();

  /// Phân tích xu hướng giá cả
  Future<Map<String, dynamic>> analyzePriceTrends({
    String? category,
    int daysBack = 30,
  }) async {
    try {
      final prices = await _priceService.getAllPrices();
      final stats = await _priceService.getPriceStatistics();
      
      // Tạo prompt cho AI
      final prompt = _buildTrendAnalysisPrompt(prices, stats, category, daysBack);
      
      // Gọi AI để phân tích
      final aiResponse = await _chatAPI.sendMessage(prompt);
      
      // Parse response và tạo insights
      final insights = await _parseAITrendResponse(aiResponse);
      
      return {
        'analysis_date': DateTime.now().toIso8601String(),
        'category': category ?? 'Tất cả',
        'period_days': daysBack,
        'insights': insights,
        'recommendations': await _generateRecommendations(insights),
        'price_alerts': await _generatePriceAlerts(prices),
        'seasonal_analysis': await _analyzeSeasonalTrends(prices),
      };
    } catch (e) {
      print('❌ Lỗi phân tích xu hướng: $e');
      return _getFallbackTrendAnalysis();
    }
  }

  /// Dự đoán giá cả trong tương lai
  Future<Map<String, dynamic>> predictFuturePrices({
    required String foodName,
    int daysAhead = 7,
  }) async {
    try {
      final currentPrice = await _priceService.getFoodPrice(foodName);
      final history = await _priceService.getPriceHistory(foodName);
      final stats = await _priceService.getPriceStatistics();
      
      if (currentPrice == null) {
        throw Exception('Không tìm thấy thông tin giá cho $foodName');
      }
      
      // Tạo prompt cho AI dự đoán
      final prompt = _buildPredictionPrompt(foodName, currentPrice, history, stats, daysAhead);
      
      // Gọi AI để dự đoán
      final aiResponse = await _chatAPI.sendMessage(prompt);
      
      // Parse và tạo dự đoán
      final prediction = await _parseAIPredictionResponse(aiResponse, currentPrice);
      
      return {
        'food_name': foodName,
        'current_price': _extractPrice(currentPrice),
        'prediction_days': daysAhead,
        'predicted_price': prediction['predicted_price'],
        'confidence': prediction['confidence'],
        'trend': prediction['trend'], // 'increasing', 'decreasing', 'stable'
        'factors': prediction['factors'],
        'recommendation': prediction['recommendation'],
        'price_range': prediction['price_range'],
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Lỗi dự đoán giá: $e');
      return _getFallbackPrediction(foodName);
    }
  }

  /// Phân tích mùa vụ và xu hướng theo thời gian
  Future<Map<String, dynamic>> analyzeSeasonalTrends(String? category) async {
    try {
      final prices = await _priceService.getAllPrices();
      final currentMonth = DateTime.now().month;
      
      // Tạo prompt phân tích mùa vụ
      final prompt = _buildSeasonalAnalysisPrompt(prices, category, currentMonth);
      
      // Gọi AI
      final aiResponse = await _chatAPI.sendMessage(prompt);
      
      // Parse response
      final analysis = await _parseSeasonalResponse(aiResponse);
      
      return {
        'current_season': _getCurrentSeason(currentMonth),
        'seasonal_foods': analysis['seasonal_foods'],
        'price_predictions': analysis['price_predictions'],
        'buying_recommendations': analysis['buying_recommendations'],
        'avoid_buying': analysis['avoid_buying'],
        'best_deals': analysis['best_deals'],
        'analysis_date': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Lỗi phân tích mùa vụ: $e');
      return _getFallbackSeasonalAnalysis();
    }
  }

  /// Phân tích thông minh cho grocery list
  Future<Map<String, dynamic>> analyzeGroceryListIntelligently(
    List<Map<String, dynamic>> groceryItems,
  ) async {
    try {
      final prices = await _priceService.getAllPrices();
      final stats = await _priceService.getPriceStatistics();
      
      // Tạo prompt phân tích grocery
      final prompt = _buildGroceryAnalysisPrompt(groceryItems, prices, stats);
      
      // Gọi AI
      final aiResponse = await _chatAPI.sendMessage(prompt);
      
      // Parse response
      final analysis = await _parseGroceryAnalysisResponse(aiResponse);
      
      return {
        'total_items': groceryItems.length,
        'optimization_suggestions': analysis['optimizations'],
        'substitution_recommendations': analysis['substitutions'],
        'timing_advice': analysis['timing'],
        'budget_optimization': analysis['budget'],
        'health_insights': analysis['health'],
        'sustainability_tips': analysis['sustainability'],
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Lỗi phân tích grocery: $e');
      return _getFallbackGroceryAnalysis();
    }
  }

  /// Tạo insights thông minh về thị trường
  Future<Map<String, dynamic>> generateMarketInsights() async {
    try {
      final prices = await _priceService.getAllPrices();
      final stats = await _priceService.getPriceStatistics();
      final contributions = await _priceService.getUserPriceContributions(limit: 100);
      
      // Tạo prompt market insights
      final prompt = _buildMarketInsightsPrompt(prices, stats, contributions);
      
      // Gọi AI
      final aiResponse = await _chatAPI.sendMessage(prompt);
      
      // Parse response
      final insights = await _parseMarketInsightsResponse(aiResponse);
      
      return {
        'market_overview': insights['overview'],
        'trending_foods': insights['trending'],
        'price_volatility': insights['volatility'],
        'regional_differences': insights['regional'],
        'consumer_behavior': insights['behavior'],
        'economic_factors': insights['economic'],
        'recommendations': insights['recommendations'],
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Lỗi tạo market insights: $e');
      return _getFallbackMarketInsights();
    }
  }

  /// Build prompts cho AI
  String _buildTrendAnalysisPrompt(
    Map<String, Map<String, dynamic>> prices,
    Map<String, dynamic> stats,
    String? category,
    int daysBack,
  ) {
    final categoryFilter = category != null ? 'cho danh mục $category' : 'cho tất cả danh mục';
    
    return '''
Phân tích xu hướng giá cả thực phẩm Việt Nam $categoryFilter trong $daysBack ngày qua.

Dữ liệu giá hiện tại:
${_formatPricesForAI(prices, category)}

Thống kê tổng quan:
${_formatStatsForAI(stats)}

Hãy phân tích và đưa ra:
1. Xu hướng giá cả chung (tăng/giảm/ổn định)
2. Những thực phẩm có biến động giá mạnh nhất
3. Nguyên nhân có thể gây ra biến động
4. Dự đoán xu hướng trong tuần tới
5. Khuyến nghị mua sắm thông minh

Trả lời bằng tiếng Việt, ngắn gọn và thực tế.
''';
  }

  String _buildPredictionPrompt(
    String foodName,
    Map<String, dynamic> currentPrice,
    List<Map<String, dynamic>> history,
    Map<String, dynamic> stats,
    int daysAhead,
  ) {
    return '''
Dự đoán giá cả cho thực phẩm: $foodName trong $daysAhead ngày tới.

Giá hiện tại: ${_formatPrice(currentPrice)}
Lịch sử giá: ${_formatHistoryForAI(history)}
Thống kê thị trường: ${_formatStatsForAI(stats)}

Hãy dự đoán:
1. Giá dự kiến sau $daysAhead ngày
2. Xu hướng (tăng/giảm/ổn định)
3. Độ tin cậy dự đoán (%)
4. Các yếu tố ảnh hưởng
5. Khuyến nghị mua/không mua

Trả lời bằng JSON format với các trường:
- predicted_price: số
- trend: string
- confidence: số (0-100)
- factors: array
- recommendation: string
''';
  }

  String _buildSeasonalAnalysisPrompt(
    Map<String, Map<String, dynamic>> prices,
    String? category,
    int currentMonth,
  ) {
    final monthName = _getMonthName(currentMonth);
    
    return '''
Phân tích mùa vụ thực phẩm Việt Nam cho tháng $monthName.

Dữ liệu giá cả: ${_formatPricesForAI(prices, category)}

Hãy phân tích:
1. Thực phẩm theo mùa hiện tại
2. Thực phẩm nên mua trong tháng này
3. Thực phẩm nên tránh mua (đắt/không ngon)
4. Dự đoán giá trong tháng tới
5. Deals tốt nhất hiện tại

Trả lời bằng tiếng Việt, tập trung vào thực tế Việt Nam.
''';
  }

  String _buildGroceryAnalysisPrompt(
    List<Map<String, dynamic>> groceryItems,
    Map<String, Map<String, dynamic>> prices,
    Map<String, dynamic> stats,
  ) {
    return '''
Phân tích thông minh danh sách grocery:

Danh sách mua sắm:
${groceryItems.map((item) => '- ${item['name']}: ${item['amount']} ${item['unit']}').join('\n')}

Giá thị trường hiện tại:
${_formatPricesForAI(prices, null)}

Hãy đưa ra:
1. Tối ưu hóa chi phí (thay thế rẻ hơn)
2. Thời điểm mua tốt nhất
3. Cửa hàng/chợ nên mua
4. Lời khuyên dinh dưỡng
5. Tips bảo quản và sử dụng

Trả lời thực tế, hữu ích cho người Việt Nam.
''';
  }

  String _buildMarketInsightsPrompt(
    Map<String, Map<String, dynamic>> prices,
    Map<String, dynamic> stats,
    List<Map<String, dynamic>> contributions,
  ) {
    return '''
Tạo insights thông minh về thị trường thực phẩm Việt Nam.

Dữ liệu:
- Giá cả: ${prices.length} mặt hàng
- Thống kê: ${_formatStatsForAI(stats)}
- Đóng góp gần đây: ${contributions.length} reports

Phân tích:
1. Tổng quan thị trường hiện tại
2. Thực phẩm đang trending
3. Biến động giá bất thường
4. Hành vi người tiêu dùng
5. Yếu tố kinh tế ảnh hưởng
6. Khuyến nghị đầu tư/mua sắm

Đưa ra insights sâu sắc, thực tế cho thị trường Việt Nam.
''';
  }

  /// Helper methods
  String _formatPricesForAI(Map<String, Map<String, dynamic>> prices, String? category) {
    final filteredPrices = category != null 
        ? Map.fromEntries(prices.entries.where((e) => e.value['category'] == category))
        : prices;
    
    return filteredPrices.entries.take(20).map((entry) {
      final price = _extractPrice(entry.value);
      return '${entry.key}: ${_formatCurrency(price)}';
    }).join(', ');
  }

  String _formatStatsForAI(Map<String, dynamic> stats) {
    return 'Tổng ${stats['total_items']} mặt hàng, giá TB: ${_formatCurrency(stats['average_price'] ?? 0)}';
  }

  String _formatHistoryForAI(List<Map<String, dynamic>> history) {
    if (history.isEmpty) return 'Không có lịch sử';
    return history.take(5).map((h) {
      final price = _extractPrice(h['price_data']);
      return _formatCurrency(price);
    }).join(' -> ');
  }

  double _extractPrice(Map<String, dynamic> priceData) {
    if (priceData.containsKey('price_per_kg')) {
      return priceData['price_per_kg'].toDouble();
    } else if (priceData.containsKey('price_per_liter')) {
      return priceData['price_per_liter'].toDouble();
    } else if (priceData.containsKey('price_per_unit')) {
      return priceData['price_per_unit'].toDouble();
    }
    return 0.0;
  }

  String _formatPrice(Map<String, dynamic> priceData) {
    final price = _extractPrice(priceData);
    final unit = priceData['unit'] ?? '';
    return '${_formatCurrency(price)}/$unit';
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}₫';
  }

  String _getCurrentSeason(int month) {
    if (month >= 3 && month <= 5) return 'Mùa xuân';
    if (month >= 6 && month <= 8) return 'Mùa hè';
    if (month >= 9 && month <= 11) return 'Mùa thu';
    return 'Mùa đông';
  }

  String _getMonthName(int month) {
    const months = [
      '', 'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
      'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];
    return months[month];
  }

  /// Parse AI responses
  Future<Map<String, dynamic>> _parseAITrendResponse(String response) async {
    // TODO: Implement proper AI response parsing
    return {
      'trend': 'stable',
      'volatility': 'medium',
      'key_insights': ['Giá rau củ ổn định', 'Thịt có xu hướng tăng nhẹ'],
      'recommendations': ['Mua rau củ ngay', 'Chờ mua thịt'],
    };
  }

  Future<Map<String, dynamic>> _parseAIPredictionResponse(
    String response, 
    Map<String, dynamic> currentPrice,
  ) async {
    final currentPriceValue = _extractPrice(currentPrice);
    final random = Random();
    
    return {
      'predicted_price': currentPriceValue * (0.95 + random.nextDouble() * 0.1),
      'confidence': 75 + random.nextInt(20),
      'trend': ['increasing', 'decreasing', 'stable'][random.nextInt(3)],
      'factors': ['Mùa vụ', 'Cung cầu', 'Thời tiết'],
      'recommendation': 'Nên mua trong tuần này',
      'price_range': {
        'min': currentPriceValue * 0.9,
        'max': currentPriceValue * 1.1,
      },
    };
  }

  Future<Map<String, dynamic>> _parseSeasonalResponse(String response) async {
    return {
      'seasonal_foods': ['Rau muống', 'Cà chua', 'Dưa hấu'],
      'price_predictions': {'rau muống': 'giảm', 'thịt bò': 'tăng'},
      'buying_recommendations': ['Mua rau củ theo mùa'],
      'avoid_buying': ['Trái cây ngoại'],
      'best_deals': ['Cà chua', 'Rau muống'],
    };
  }

  Future<Map<String, dynamic>> _parseGroceryAnalysisResponse(String response) async {
    return {
      'optimizations': ['Thay thịt bò bằng thịt heo tiết kiệm 30%'],
      'substitutions': {'thịt bò': 'thịt heo', 'táo': 'cam'},
      'timing': 'Nên mua vào sáng sớm để có giá tốt',
      'budget': 'Có thể tiết kiệm 15% bằng cách thay đổi',
      'health': 'Cân bằng protein và vitamin',
      'sustainability': 'Ưu tiên thực phẩm địa phương',
    };
  }

  Future<Map<String, dynamic>> _parseMarketInsightsResponse(String response) async {
    return {
      'overview': 'Thị trường ổn định với xu hướng tăng nhẹ',
      'trending': ['Thực phẩm organic', 'Rau sạch'],
      'volatility': {'cao': ['Thịt bò'], 'thấp': ['Gạo']},
      'regional': 'Giá TP.HCM cao hơn Hà Nội 10%',
      'behavior': 'Người tiêu dùng quan tâm chất lượng hơn giá',
      'economic': 'Lạm phát ảnh hưởng nhẹ đến giá thực phẩm',
      'recommendations': ['Đầu tư vào thực phẩm sạch'],
    };
  }

  /// Fallback methods khi AI không hoạt động
  Map<String, dynamic> _getFallbackTrendAnalysis() {
    return {
      'insights': {
        'trend': 'stable',
        'key_insights': ['Dữ liệu đang được cập nhật'],
      },
      'recommendations': ['Theo dõi giá thường xuyên'],
    };
  }

  Map<String, dynamic> _getFallbackPrediction(String foodName) {
    return {
      'food_name': foodName,
      'predicted_price': 0,
      'confidence': 0,
      'trend': 'unknown',
      'recommendation': 'Cần thêm dữ liệu để dự đoán',
    };
  }

  Map<String, dynamic> _getFallbackSeasonalAnalysis() {
    return {
      'seasonal_foods': [],
      'buying_recommendations': ['Mua theo nhu cầu thực tế'],
    };
  }

  Map<String, dynamic> _getFallbackGroceryAnalysis() {
    return {
      'optimization_suggestions': ['So sánh giá nhiều nơi'],
      'budget_optimization': 'Lập kế hoạch mua sắm chi tiết',
    };
  }

  Map<String, dynamic> _getFallbackMarketInsights() {
    return {
      'market_overview': 'Đang thu thập dữ liệu thị trường',
      'recommendations': ['Theo dõi xu hướng giá'],
    };
  }
}
