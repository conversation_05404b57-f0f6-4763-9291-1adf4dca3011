��{ %   e x t e n d s   ' b a s e . h t m l '   % } 
 
 
 
 { %   b l o c k   t i t l e   % } S �a   m � n   n   -   { {   f o o d . n a m e   } } { %   e n d b l o c k   % } 
 
 
 
 { %   b l o c k   c o n t e n t   % } 
 
 < d i v   c l a s s = " r o w   m b - 4 " > 
 
         < d i v   c l a s s = " c o l - m d - 8 " > 
 
                 < n a v   a r i a - l a b e l = " b r e a d c r u m b " > 
 
                         < o l   c l a s s = " b r e a d c r u m b " > 
 
                                 < l i   c l a s s = " b r e a d c r u m b - i t e m " > < a   h r e f = " / " > T r a n g   c h �< / a > < / l i > 
 
                                 < l i   c l a s s = " b r e a d c r u m b - i t e m " > < a   h r e f = " / f o o d " > M � n   n < / a > < / l i > 
 
                                 < l i   c l a s s = " b r e a d c r u m b - i t e m " > < a   h r e f = " / f o o d / { {   f o o d . i d   } } " > { {   f o o d . n a m e   } } < / a > < / l i > 
 
                                 < l i   c l a s s = " b r e a d c r u m b - i t e m   a c t i v e "   a r i a - c u r r e n t = " p a g e " > S �a < / l i > 
 
                         < / o l > 
 
                 < / n a v > 
 
                 < h 1   c l a s s = " m b - 3 " > S �a   m � n   n :   { {   f o o d . n a m e   } } < / h 1 > 
 
         < / d i v > 
 
 < / d i v > 
 
 
 
 < d i v   c l a s s = " r o w " > 
 
         < d i v   c l a s s = " c o l - m d - 1 2 " > 
 
                 < d i v   c l a s s = " c a r d " > 
 
                         < d i v   c l a s s = " c a r d - h e a d e r " > 
 
                                 < h 5   c l a s s = " m b - 0 " > T h � n g   t i n   m � n   n < / h 5 > 
 
                         < / d i v > 
 
                         < d i v   c l a s s = " c a r d - b o d y " > 
 
                                 < f o r m   i d = " f o o d - f o r m "   a c t i o n = " / f o o d / e d i t / { {   f o o d . i d   } } "   m e t h o d = " p o s t "   c l a s s = " n e e d s - v a l i d a t i o n " > 
 
                                         < d i v   c l a s s = " r o w " > 
 
                                                 < d i v   c l a s s = " c o l - m d - 6 " > 
 
                                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                                 < l a b e l   f o r = " n a m e "   c l a s s = " f o r m - l a b e l " > T � n   m � n   n   < s p a n   c l a s s = " t e x t - d a n g e r " > * < / s p a n > < / l a b e l > 
 
                                                                 < i n p u t   t y p e = " t e x t "   c l a s s = " f o r m - c o n t r o l "   i d = " n a m e "   n a m e = " n a m e "   v a l u e = " { {   f o o d . n a m e   } } "   r e q u i r e d > 
 
                                                         < / d i v > 
 
                                                 < / d i v > 
 
                                                 < d i v   c l a s s = " c o l - m d - 6 " > 
 
                                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                                 < l a b e l   f o r = " p r e p a r a t i o n _ t i m e "   c l a s s = " f o r m - l a b e l " > T h �i   g i a n   c h u �n   b �< / l a b e l > 
 
                                                                 < i n p u t   t y p e = " t e x t "   c l a s s = " f o r m - c o n t r o l "   i d = " p r e p a r a t i o n _ t i m e "   n a m e = " p r e p a r a t i o n _ t i m e "   v a l u e = " { {   f o o d . p r e p a r a t i o n _ t i m e   } } "   p l a c e h o l d e r = " V D :   3 0   p h � t " > 
 
                                                         < / d i v > 
 
                                                 < / d i v > 
 
                                         < / d i v > 
 
                                         
 
                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                 < l a b e l   f o r = " d e s c r i p t i o n "   c l a s s = " f o r m - l a b e l " > M �   t �< / l a b e l > 
 
                                                 < t e x t a r e a   c l a s s = " f o r m - c o n t r o l "   i d = " d e s c r i p t i o n "   n a m e = " d e s c r i p t i o n "   r o w s = " 3 " > { {   f o o d . d e s c r i p t i o n   } } < / t e x t a r e a > 
 
                                         < / d i v > 
 
                                         
 
                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                 < l a b e l   f o r = " h e a l t h _ b e n e f i t s "   c l a s s = " f o r m - l a b e l " > L �i   � c h   s �c   k h �e < / l a b e l > 
 
                                                 < t e x t a r e a   c l a s s = " f o r m - c o n t r o l "   i d = " h e a l t h _ b e n e f i t s "   n a m e = " h e a l t h _ b e n e f i t s "   r o w s = " 2 " > { {   f o o d . h e a l t h _ b e n e f i t s   } } < / t e x t a r e a > 
 
                                         < / d i v > 
 
                                         
 
                                         < d i v   c l a s s = " r o w " > 
 
                                                 < d i v   c l a s s = " c o l - m d - 3 " > 
 
                                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                                 < l a b e l   f o r = " c a l o r i e s _ i n p u t "   c l a s s = " f o r m - l a b e l " > C a l o r i e s   ( k c a l ) < / l a b e l > 
 
                                                                 < i n p u t   t y p e = " n u m b e r "   c l a s s = " f o r m - c o n t r o l "   i d = " c a l o r i e s _ i n p u t "   n a m e = " c a l o r i e s "   m i n = " 0 "   v a l u e = " { {   f o o d . n u t r i t i o n . c a l o r i e s   } } " > 
 
                                                         < / d i v > 
 
                                                 < / d i v > 
 
                                                 < d i v   c l a s s = " c o l - m d - 3 " > 
 
                                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                                 < l a b e l   f o r = " p r o t e i n _ i n p u t "   c l a s s = " f o r m - l a b e l " > P r o t e i n   ( g ) < / l a b e l > 
 
                                                                 < i n p u t   t y p e = " n u m b e r "   c l a s s = " f o r m - c o n t r o l "   i d = " p r o t e i n _ i n p u t "   n a m e = " p r o t e i n "   m i n = " 0 "   s t e p = " 0 . 1 "   v a l u e = " { {   f o o d . n u t r i t i o n . p r o t e i n   } } " > 
 
                                                         < / d i v > 
 
                                                 < / d i v > 
 
                                                 < d i v   c l a s s = " c o l - m d - 3 " > 
 
                                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                                 < l a b e l   f o r = " f a t _ i n p u t "   c l a s s = " f o r m - l a b e l " > C h �t   b � o   ( g ) < / l a b e l > 
 
                                                                 < i n p u t   t y p e = " n u m b e r "   c l a s s = " f o r m - c o n t r o l "   i d = " f a t _ i n p u t "   n a m e = " f a t "   m i n = " 0 "   s t e p = " 0 . 1 "   v a l u e = " { {   f o o d . n u t r i t i o n . f a t   } } " > 
 
                                                         < / d i v > 
 
                                                 < / d i v > 
 
                                                 < d i v   c l a s s = " c o l - m d - 3 " > 
 
                                                         < d i v   c l a s s = " f o r m - g r o u p   m b - 3 " > 
 
                                                                 < l a b e l   f o r = " c a r b s _ i n p u t "   c l a s s = " f o r m - l a b e l " > C a r b s   ( g ) < / l a b e l > 
 
                                                                 < i n p u t   t y p e = " n u m b e r "   c l a s s = " f o r m - c o n t r o l "   i d = " c a r b s _ i n p u t "   n a m e = " c a r b s "   m i n = " 0 "   s t e p = " 0 . 1 "   v a l u e = " { {   f o o d . n u t r i t i o n . c a r b s   } } " > 
 
                                                         < / d i v > 
 
                                                 < / d i v > 
 
                                         < / d i v > 
 
                                         
 
                                         < d i v   c l a s s = " r o w   m b - 3 " > 
 
                                                 < d i v   c l a s s = " c o l - m d - 1 2 " > 
 
                                                         < l a b e l   c l a s s = " f o r m - l a b e l " > N g u y � n   l i �u < / l a b e l > 
 
                                                         < d i v   i d = " i n g r e d i e n t s - c o n t a i n e r " > 
 
                                                                 { %   f o r   i n g r e d i e n t   i n   f o o d . i n g r e d i e n t s   % } 
 
                                                                 < d i v   c l a s s = " r o w   m b - 2 " > 
 
                                                                         < d i v   c l a s s = " c o l - m d - 6 " > 
 
                                                                                 < i n p u t   t y p e = " t e x t "   c l a s s = " f o r m - c o n t r o l "   n a m e = " i n g r e d i e n t _ n a m e _ { {   l o o p . i n d e x 0   } } "   v a l u e = " { {   i n g r e d i e n t . n a m e   } } "   p l a c e h o l d e r = " T � n   n g u y � n   l i �u " > 
 
                                                                         < / d i v > 
 
                                                                         < d i v   c l a s s = " c o l - m d - 4 " > 
 
                                                                                 < i n p u t   t y p e = " t e x t "   c l a s s = " f o r m - c o n t r o l "   n a m e = " i n g r e d i e n t _ a m o u n t _ { {   l o o p . i n d e x 0   } } "   v a l u e = " { {   i n g r e d i e n t . a m o u n t   } } "   p l a c e h o l d e r = " S �  l ��n g " > 
 
                                                                         < / d i v > 
 
                                                                         < d i v   c l a s s = " c o l - m d - 2 " > 
 
                                                                                 < b u t t o n   t y p e = " b u t t o n "   c l a s s = " b t n   b t n - d a n g e r "   o n c l i c k = " r e m o v e I n g r e d i e n t I n p u t ( t h i s ) " > X � a < / b u t t o n > 
 
                                                                         < / d i v > 
 
                                                                 < / d i v > 
 
                                                                 { %   e n d f o r   % } 
 
                                                         < / d i v > 
 
                                                         < b u t t o n   t y p e = " b u t t o n "   c l a s s = " b t n   b t n - s m   b t n - o u t l i n e - p r i m a r y   m t - 2 "   o n c l i c k = " a d d I n g r e d i e n t I n p u t ( ) " > +   T h � m   n g u y � n   l i �u < / b u t t o n > 
 
                                                 < / d i v > 
 
                                         < / d i v > 
 
                                         
 
                                         < d i v   c l a s s = " r o w   m b - 3 " > 
 
                                                 < d i v   c l a s s = " c o l - m d - 1 2 " > 
 
                                                         < l a b e l   c l a s s = " f o r m - l a b e l " > C � c   b ��c   c h �  b i �n < / l a b e l > 
 
                                                         < d i v   i d = " p r e p a r a t i o n - c o n t a i n e r " > 
 
                                                                 { %   f o r   s t e p   i n   f o o d . p r e p a r a t i o n   % } 
 
                                                                 < d i v   c l a s s = " r o w   m b - 2 " > 
 
                                                                         < d i v   c l a s s = " c o l - m d - 1 0 " > 
 
                                                                                 < i n p u t   t y p e = " t e x t "   c l a s s = " f o r m - c o n t r o l "   n a m e = " p r e p a r a t i o n _ s t e p _ { {   l o o p . i n d e x 0   } } "   v a l u e = " { {   s t e p   } } "   p l a c e h o l d e r = " B ��c   c h �  b i �n " > 
 
                                                                         < / d i v > 
 
                                                                         < d i v   c l a s s = " c o l - m d - 2 " > 
 
                                                                                 < b u t t o n   t y p e = " b u t t o n "   c l a s s = " b t n   b t n - d a n g e r "   o n c l i c k = " r e m o v e P r e p a r a t i o n S t e p ( t h i s ) " > X � a < / b u t t o n > 
 
                                                                         < / d i v > 
 
                                                                 < / d i v > 
 
                                                                 { %   e n d f o r   % } 
 
                                                         < / d i v > 
 
                                                         < b u t t o n   t y p e = " b u t t o n "   c l a s s = " b t n   b t n - s m   b t n - o u t l i n e - p r i m a r y   m t - 2 "   o n c l i c k = " a d d P r e p a r a t i o n S t e p ( ) " > +   T h � m   b ��c < / b u t t o n > 
 
                                                 < / d i v > 
 
                                         < / d i v > 
 
                                         
 
                                         < d i v   c l a s s = " t e x t - c e n t e r   m t - 4 " > 
 
                                                 < b u t t o n   t y p e = " s u b m i t "   c l a s s = " b t n   b t n - p r i m a r y " > L �u   t h a y   �i < / b u t t o n > 
 
                                                 < a   h r e f = " / f o o d / { {   f o o d . i d   } } "   c l a s s = " b t n   b t n - o u t l i n e - s e c o n d a r y   m s - 2 " > H �y   b �< / a > 
 
                                         < / d i v > 
 
                                 < / f o r m > 
 
                         < / d i v > 
 
                 < / d i v > 
 
         < / d i v > 
 
 < / d i v > 
 
 { %   e n d b l o c k   % } 
 
 
 
 { %   b l o c k   e x t r a _ j s   % } 
 
 < s c r i p t > 
 
         d o c u m e n t . a d d E v e n t L i s t e n e r ( ' D O M C o n t e n t L o a d e d ' ,   f u n c t i o n ( )   { 
 
                 / /   C h �  t h � m   n g u y � n   l i �u   m �u   n �u   k h � n g   c �   n g u y � n   l i �u 
 
                 c o n s t   i n g r e d i e n t s C o n t a i n e r   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' i n g r e d i e n t s - c o n t a i n e r ' ) ; 
 
                 i f   ( i n g r e d i e n t s C o n t a i n e r   & &   i n g r e d i e n t s C o n t a i n e r . c h i l d r e n . l e n g t h   = = =   0 )   { 
 
                         a d d I n g r e d i e n t I n p u t ( ) ; 
 
                 } 
 
                 
 
                 / /   C h �  t h � m   b ��c   c h �  b i �n   m �u   n �u   k h � n g   c �   b ��c   n � o 
 
                 c o n s t   p r e p a r a t i o n C o n t a i n e r   =   d o c u m e n t . g e t E l e m e n t B y I d ( ' p r e p a r a t i o n - c o n t a i n e r ' ) ; 
 
                 i f   ( p r e p a r a t i o n C o n t a i n e r   & &   p r e p a r a t i o n C o n t a i n e r . c h i l d r e n . l e n g t h   = = =   0 )   { 
 
                         a d d P r e p a r a t i o n S t e p ( ) ; 
 
                 } 
 
         } ) ; 
 
 < / s c r i p t > 
 
 { %   e n d b l o c k   % } 
 
 