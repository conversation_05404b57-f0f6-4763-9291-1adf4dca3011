/* Tùy chỉnh màu sắc và font */
:root {
  --primary-color: #28a745;
  --secondary-color: #6c757d;
  --accent-color: #ffc107;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.navbar {
  box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.card {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0,0,0,.05);
  transition: transform 0.3s;
}

.card:hover {
  transform: translateY(-5px);
}

.footer {
  border-top: 1px solid #dee2e6;
}

/* Tùy chỉnh cho trang danh sách món ăn */
.nutrition-info {
  border-top: 1px solid #dee2e6;
  padding-top: 10px;
  margin-top: 10px;
}

/* Style cho form */
.form-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 15px rgba(0,0,0,.05);
}

/* Style cho nút */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* Style cho trang chi tiết món ăn */
.food-detail-card {
  background-color: #fff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,.1);
}

.food-header {
  background-color: var(--primary-color);
  color: white;
  padding: 20px;
}

.ingredient-list {
  list-style-type: none;
  padding-left: 0;
}

.ingredient-list li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.preparation-step {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-left: 3px solid var(--accent-color);
  border-radius: 5px;
}

/* Admin Dashboard Styles */
.sidebar {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.sidebar .nav-link {
  color: rgba(255,255,255,0.8);
  padding: 12px 20px;
  border-radius: 8px;
  margin: 2px 10px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
  background-color: rgba(255,255,255,0.2);
  color: white;
  transform: translateX(5px);
}

.sidebar .nav-link i {
  width: 20px;
  margin-right: 10px;
  text-align: center;
}

.main-content {
  background-color: #f8f9fa;
  min-height: 100vh;
  margin-left: 16.66667%; /* Offset for sidebar */
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }
  .sidebar {
    position: relative;
  }
}

/* Stat Cards */
.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card-success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card-info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Status Indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

/* Chart Containers */
.chart-area {
  position: relative;
  height: 300px;
}

.chart-pie {
  position: relative;
  height: 250px;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
  background-color: rgba(0,123,255,0.05);
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
}

/* Badge Styles */
.badge {
  font-size: 0.75em;
  padding: 0.375rem 0.75rem;
}

/* Modal Enhancements */
.modal-content {
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  border-radius: 15px 15px 0 0;
}

/* Button Group Enhancements */
.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

/* Pagination Enhancements */
.pagination .page-link {
  border: none;
  color: #667eea;
  margin: 0 2px;
  border-radius: 8px;
}

.pagination .page-item.active .page-link {
  background-color: #667eea;
  border-color: #667eea;
}

.pagination .page-link:hover {
  background-color: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* Form Enhancements */
.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Alert Enhancements */
.alert {
  border: none;
  border-radius: 10px;
}

/* Loading Spinner */
.spinner-border {
  color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    position: relative;
    min-height: auto;
    margin-bottom: 1rem;
  }

  .main-content {
    margin-left: 0;
  }

  .stat-card .h5 {
    font-size: 1.2rem;
  }

  .card-body {
    padding: 1rem;
  }

  .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .chart-area,
  .chart-pie,
  .chart-bar,
  .chart-doughnut {
    height: 200px;
  }
}

@media (max-width: 576px) {
  .stat-card .h5 {
    font-size: 1.1rem;
  }

  .card-body {
    padding: 0.75rem;
  }

  .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .chart-area,
  .chart-pie,
  .chart-bar,
  .chart-doughnut {
    height: 180px;
  }

  .sidebar .nav-link {
    padding: 8px 15px;
    font-size: 0.9rem;
  }

  .sidebar .nav-link i {
    width: 16px;
    margin-right: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #ffffff;
  }

  .main-content {
    background-color: #1a1a1a;
  }

  .card {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  .table {
    --bs-table-bg: #2d2d2d;
    --bs-table-color: #ffffff;
  }

  .form-control {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }

  .form-control:focus {
    background-color: #2d2d2d;
    border-color: #667eea;
    color: #ffffff;
  }
}

/* Smooth transitions */
* {
  transition: all 0.3s ease;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hover effects */
.card:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5a6fd8;
}

/* Notification animations */
.alert {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Focus states for accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .sidebar,
  .btn,
  .dropdown {
    display: none !important;
  }

  .main-content {
    margin-left: 0 !important;
  }

  .card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}