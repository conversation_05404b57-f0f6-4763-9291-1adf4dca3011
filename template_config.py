from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import os

def configure_templates(app: FastAPI):
    # Đường dẫn gốc của dự án
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Mount thư mục static để phục vụ các file CSS, JS, hình ảnh
    app.mount("/static", StaticFiles(directory=os.path.join(base_dir, "static")), name="static")
    
    # Khởi tạo Jinja2Templates cho thư mục templates
    templates = Jinja2Templates(directory=os.path.join(base_dir, "templates"))
    
    return templates 