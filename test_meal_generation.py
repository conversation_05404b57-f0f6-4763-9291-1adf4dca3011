#!/usr/bin/env python3
"""
Script kiểm tra tạo meal plan với các thay đổi mới
"""
import os
import json
from groq_integration import GroqService  # Enhanced version  # Fixed version

def test_meal_generation():
    """Test meal generation with the fixed implementation"""
    print("=== Testing meal generation with fixed implementation ===")
    
    # Ensure we have the API key
    if not os.getenv("GROQ_API_KEY"):
        api_key = input("Enter your Groq API key: ")
        os.environ["GROQ_API_KEY"] = api_key
    
    # Create the service
    service = GroqService()
    
    # Test parameters
    meal_types = ["bữa sáng", "bữa trưa", "bữa tối"]
    
    for meal_type in meal_types:
        print(f"\n--- Testing meal type: {meal_type} ---")
        
        # Generate meal suggestions
        meals = service.generate_meal_suggestions(
            calories_target=400,
            protein_target=25,
            fat_target=15,
            carbs_target=40,
            meal_type=meal_type,
            preferences=["healthy", "balanced"],
            allergies=["seafood"],
            use_ai=True
        )
        
        # Print results
        print(f"Generated {len(meals)} meal suggestions:")
        for i, meal in enumerate(meals):
            print(f"\nMeal {i+1}: {meal.get('name', 'Unknown')}")
            print(f"Ingredients: {', '.join([ing for ing in meal.get('ingredients', [])])}")
            print(f"Nutrition: {meal.get('nutrition', {})}")
            print(f"Recipe: {meal.get('recipe', 'No recipe provided')[:100]}...")

if __name__ == "__main__":
    test_meal_generation() 