name: openfood
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  http: ^1.4.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  flutter_localizations:
    sdk: flutter
  provider: ^6.0.5
  shared_preferences: ^2.5.3
  intl: ^0.19.0
  permission_handler: ^12.0.0+1
  app_settings: ^6.1.1
  image: ^4.1.3
  image_picker: ^1.0.7

  # Đăng nhập với Google
  google_sign_in: ^6.2.1

  # Các package hiện có
  record: ^4.4.4
  path_provider: ^2.1.1
  audio_waveforms: ^1.0.4
  flutter_barcode_scanner: ^2.0.0
  sqflite: ^2.3.0
  uuid: ^3.0.7
  cached_network_image: ^3.3.0

  # Package mới cho AI và cơ sở dữ liệu thực phẩm (đã điều chỉnh)
  flutter_dotenv: ^5.1.0  # Quản lý API key

  # Package nhận diện thực phẩm
  # Chỉ sử dụng ML Kit, tránh xung đột với TensorFlow
  google_mlkit_image_labeling: ^0.11.0

  # Cơ sở dữ liệu thực phẩm
  hive: ^2.2.3  # Lưu cache dữ liệu thực phẩm
  hive_flutter: ^1.1.0

  # UI/UX
  shimmer: ^3.0.0  # Hiệu ứng loading
  connectivity_plus: ^4.0.2  # Kiểm tra kết nối
  google_fonts: ^5.1.0  # Google Fonts

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_staggered_animations: ^1.1.1

  # Added for Firebase
  firebase_core: ^2.32.0
  firebase_auth: ^4.16.0
  cloud_firestore: ^4.17.5
  cloud_functions: ^4.3.4
  fl_chart: ^0.71.0
  package_info_plus: ^8.3.0
  intl_phone_field: ^3.2.0

  # Added for smooth page indicator
  smooth_page_indicator: ^1.0.0+2

  # Added for pin code fields
  flutter_slidable: ^3.0.1
  location: ^5.0.3
  pin_code_fields: ^8.0.1

  # Added from the code block
  url_launcher: ^6.1.14
  firebase_storage: ^11.6.5
  flutter_cache_manager: ^3.3.1
  flutter_secure_storage: ^9.0.0
  horizontal_picker: ^1.2.0
  video_player: ^2.8.1
  youtube_player_flutter: ^9.0.1
  grouped_list: ^5.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
