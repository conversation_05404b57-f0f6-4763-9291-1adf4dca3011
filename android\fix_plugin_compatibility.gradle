/**
 * <PERSON><PERSON><PERSON> đ<PERSON> sửa vấn đề tương thích Java/Kotlin trong các plugin Flutter Android
 */

// <PERSON>h sách các plugin cầ<PERSON> đ<PERSON><PERSON><PERSON> sửa
def pluginsToFix = [
    'shared_preferences_android',
    'audio_waveforms',
    'app_settings',
    'firebase_core',
    'firebase_auth',
    'cloud_firestore',
    'cloud_functions',
    'location'
    // Thêm các plugin khác nếu cần
]

// Áp dụng sửa đổi cho mỗi plugin
subprojects { subproject ->
    if (pluginsToFix.contains(subproject.name)) {
        subproject.afterEvaluate {
            if (subproject.plugins.hasPlugin('com.android.library') || subproject.plugins.hasPlugin('com.android.application')) {
                // Thiết lập Java 8 cho plugin này
                subproject.android {
                    compileOptions {
                        sourceCompatibility JavaVersion.VERSION_1_8
                        targetCompatibility JavaVersion.VERSION_1_8
                    }
                }
                
                // Nếu plugin sử dụ<PERSON>, đ<PERSON><PERSON> bộ Kotlin với Java 8
                if (subproject.plugins.hasPlugin('kotlin-android') || subproject.plugins.hasPlugin('org.jetbrains.kotlin.android')) {
                    subproject.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                        kotlinOptions {
                            jvmTarget = "1.8"
                        }
                    }
                }
                
                println "✅ Đã sửa cấu hình Java/Kotlin cho plugin: ${subproject.name}"
            }
        }
    }
} 