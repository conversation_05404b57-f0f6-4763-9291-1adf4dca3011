{% extends 'base.html' %}

{% block title %}Thêm món ăn mới - OpenFood{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                <li class="breadcrumb-item"><a href="/food">Món ăn</a></li>
                <li class="breadcrumb-item active" aria-current="page">Thêm món ăn mới</li>
            </ol>
        </nav>
        <h1 class="mb-3">Thêm món ăn mới</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Tạo món ăn tự động với AI</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="meal_type" class="form-label">Loại bữa ăn</label>
                            <select id="meal_type" class="form-select">
                                <option value="breakfast">Bữa sáng</option>
                                <option value="lunch">Bữa trưa</option>
                                <option value="dinner">Bữa tối</option>
                                <option value="snack">Bữa phụ</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="cuisine_style" class="form-label">Phong cách ẩm thực</label>
                            <select id="cuisine_style" class="form-select">
                                <option value="vietnamese">Việt Nam</option>
                                <option value="asian">Châu Á</option>
                                <option value="western">Phương Tây</option>
                                <option value="fusion">Fusion</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="calories" class="form-label">Calories (kcal)</label>
                            <input type="number" id="calories" class="form-control" value="400" min="100" max="2000">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="protein" class="form-label">Protein (g)</label>
                            <input type="number" id="protein" class="form-control" value="25" min="5" max="100">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="fat" class="form-label">Chất béo (g)</label>
                            <input type="number" id="fat" class="form-control" value="15" min="5" max="100">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="carbs" class="form-label">Carbs (g)</label>
                            <input type="number" id="carbs" class="form-control" value="40" min="5" max="200">
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <button id="generate-btn" class="btn btn-success" onclick="generateRandomMeal()">
                        <span class="me-1">🤖</span> Tạo món ăn với AI
                    </button>
                    <div id="generate-status" class="mt-2 text-info"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thông tin món ăn</h5>
            </div>
            <div class="card-body">
                <form id="food-form" action="/food/create" method="post" class="needs-validation">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="name" class="form-label">Tên món ăn <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="preparation_time" class="form-label">Thời gian chuẩn bị</label>
                                <input type="text" class="form-control" id="preparation_time" name="preparation_time" placeholder="VD: 30 phút">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">Mô tả</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="health_benefits" class="form-label">Lợi ích sức khỏe</label>
                        <textarea class="form-control" id="health_benefits" name="health_benefits" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="calories_input" class="form-label">Calories (kcal)</label>
                                <input type="number" class="form-control" id="calories_input" name="calories" min="0" value="400">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="protein_input" class="form-label">Protein (g)</label>
                                <input type="number" class="form-control" id="protein_input" name="protein" min="0" step="0.1" value="25">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="fat_input" class="form-label">Chất béo (g)</label>
                                <input type="number" class="form-control" id="fat_input" name="fat" min="0" step="0.1" value="15">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="carbs_input" class="form-label">Carbs (g)</label>
                                <input type="number" class="form-control" id="carbs_input" name="carbs" min="0" step="0.1" value="40">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Nguyên liệu</label>
                            <div id="ingredients-container"></div>
                            <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addIngredientInput()">+ Thêm nguyên liệu</button>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Các bước chế biến</label>
                            <div id="preparation-container"></div>
                            <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addPreparationStep()">+ Thêm bước</button>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary">Lưu món ăn</button>
                        <a href="/food" class="btn btn-outline-secondary ms-2">Hủy bỏ</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        addIngredientInput();
        addPreparationStep();
    });
</script>
{% endblock %}
