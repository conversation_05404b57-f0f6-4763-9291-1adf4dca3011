{"timestamp": "2025-06-13T12:14:24.073744", "base_url": "http://localhost:8000", "total_time": 60.33322787284851, "success_rate": 25.0, "test_results": {"Enhanced Groq Integration": {"name": "Enhanced Groq <PERSON>", "success": true, "duration": 7.083608627319336, "stdout": null, "stderr": "", "return_code": 0}, "Comprehensive API Tests": {"name": "Comprehensive API Tests", "success": false, "duration": 0.4579007625579834, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\backend\\comprehensive_test_suite.py\", line 11, in <module>\n    import pytest\nModuleNotFoundError: No module named 'pytest'\n", "return_code": 1}, "Database Integration": {"name": "Database Integration", "success": false, "duration": 49.099849462509155, "stdout": null, "stderr": "", "return_code": 1}, "Endpoint Stress Tests": {"name": "Endpoint Stress Tests", "success": false, "duration": 0.0075266361236572266, "stdout": "", "stderr": "Error running async test: No module named 'aiohttp'", "return_code": -1}}}