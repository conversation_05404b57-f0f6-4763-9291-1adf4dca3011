allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Thêm classpath dependencies cho Google Services
buildscript {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        // Đảm bảo phiên bản n<PERSON> tương thích với Gradle trong wrapper.properties
        classpath 'com.android.tools.build:gradle:8.2.1'
        classpath 'com.google.gms:google-services:4.3.15' // Dùng phiên bản ổn định
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.22'
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    
    // Thêm namespace cho các module Android và thiết lập Java/Kotlin target
    plugins.withId('com.android.library') {
        android {
            if (!project.hasProperty('android.namespace') && !hasProperty('namespace')) {
                namespace project.group ?: project.name
            }
            
            // Thiết lập Java 17 cho tất cả các module
            compileOptions {
                sourceCompatibility JavaVersion.VERSION_17
                targetCompatibility JavaVersion.VERSION_17
            }
        }
        
        // Đảm bảo Kotlin tương thích với Java 17
        plugins.withId('org.jetbrains.kotlin.android') {
            tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                kotlinOptions {
                    jvmTarget = "17"
                }
            }
        }
    }
    
    plugins.withId('com.android.application') {
        android {
            if (!project.hasProperty('android.namespace') && !hasProperty('namespace')) {
                namespace project.group ?: project.name
            }
            
            // Thiết lập Java 17 cho tất cả các module
            compileOptions {
                sourceCompatibility JavaVersion.VERSION_17
                targetCompatibility JavaVersion.VERSION_17
            }
        }
        
        // Đảm bảo Kotlin tương thích với Java 17
        plugins.withId('org.jetbrains.kotlin.android') {
            tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                kotlinOptions {
                    jvmTarget = "17"
                }
            }
        }
    }
}

subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

// Áp dụng script để sửa tương thích Java/Kotlin trong các plugin Flutter
apply from: 'fix_plugin_compatibility.gradle'
